"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useDebounce } from "@/hooks/use-debounce";
import { useAddHubMember, useHubMembers, User, useRemoveMember, useUpdateMemberRole } from "@/hooks/use-hub-members";
import { useFilteredUserSearch } from "@/hooks/use-user-search";
import {
  ArrowLeft,
  Crown,
  Loader2,
  MoreHorizontal,
  PlusCircle,
  Search,
  Shield,
  Trash,
  UserPlus,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

export function MembersClient({ hubId }: { hubId: string }) {
  // State for the dialog
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedRole, setSelectedRole] = useState<"MODERATOR" | "MANAGER">("MODERATOR");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // Fetch hub members using Tanstack Query
  const { data: members } = useHubMembers(hubId);

  // Search for users using Tanstack Query
  const {
    data: searchResults = [],
    isLoading: isSearching
  } = useFilteredUserSearch(
    debouncedSearchQuery,
    members,
    { enabled: debouncedSearchQuery.length > 0 }
  );

  // Mutations for member management
  const addMemberMutation = useAddHubMember(hubId);
  const updateRoleMutation = useUpdateMemberRole(hubId);
  const removeMemberMutation = useRemoveMember(hubId);

  // Handle adding a member
  const handleAddMember = () => {
    if (!selectedUser) return;

    addMemberMutation.mutate(
      {
        userId: selectedUser.id,
        role: selectedRole,
      },
      {
        onSuccess: () => {
          // Reset the form
          setSelectedUser(null);
          setSelectedRole("MODERATOR");
          setSearchQuery("");
          setIsDialogOpen(false);
        },
      }
    );
  };

  // Handle updating a member's role
  const handleUpdateRole = (memberId: string, newRole: "MODERATOR" | "MANAGER") => {
    updateRoleMutation.mutate({ memberId, role: newRole });
  };

  // Handle removing a member
  const handleRemoveMember = (memberId: string) => {
    removeMemberMutation.mutate(memberId);
  };

  return (
    <>
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6 items-center">
        <div className="flex items-center" />
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-600/80 hover:to-purple-600/80 border-none w-full sm:w-auto">
              <UserPlus className="h-4 w-4 mr-2" />
              Add Member
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-gradient-to-b from-gray-900/95 to-gray-950/95 backdrop-blur-md border border-gray-800/50">
            <DialogHeader>
              <DialogTitle>Add New Member</DialogTitle>
              <DialogDescription>
                Search for a user to add as a moderator or manager.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 py-4">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  placeholder="Search users by name..."
                  className="pl-8 bg-gray-900/50 border-gray-800 hover:bg-gray-900 transition-colors"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <div className="max-h-60 overflow-y-auto space-y-2">
                {isSearching ? (
                  <div className="flex justify-center py-4">
                    <Loader2 className="h-6 w-6 animate-spin text-primary" />
                  </div>
                ) : searchResults.length > 0 ? (
                  searchResults.map((user) => (
                    <div
                      key={user.id}
                      className={`flex items-center gap-3 p-2 rounded-md cursor-pointer ${
                        selectedUser?.id === user.id
                          ? "bg-indigo-900/30 border border-indigo-700/30"
                          : "hover:bg-gray-800/50"
                      }`}
                      onClick={() => setSelectedUser(user)}
                    >
                      <Image
                        src={
                          user.image ||
                          "https://api.dicebear.com/7.x/shapes/svg?seed=user"
                        }
                        alt={user.name || "User"}
                        width={32}
                        height={32}
                        className="rounded-full"
                        unoptimized
                      />
                      <div className="flex-1">
                        <div className="font-medium">{user.name}</div>
                      </div>
                    </div>
                  ))
                ) : searchQuery ? (
                  <p className="text-center text-gray-400 py-4">
                    No users found
                  </p>
                ) : (
                  <p className="text-center text-gray-400 py-4">
                    Start typing to search for users
                  </p>
                )}
              </div>

              {selectedUser && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Selected User:</span>
                    <span className="text-sm">{selectedUser.name}</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Role:</span>
                    <Select
                      value={selectedRole}
                      onValueChange={(value) =>
                        setSelectedRole(value as "MODERATOR" | "MANAGER")
                      }
                    >
                      <SelectTrigger className="w-32 bg-gray-900/50 border-gray-800 hover:bg-gray-900 transition-colors">
                        <SelectValue placeholder="Select role" />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-900 border-gray-800">
                        <SelectItem value="MODERATOR">Moderator</SelectItem>
                        <SelectItem value="MANAGER">Manager</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setIsDialogOpen(false);
                  setSelectedUser(null);
                  setSearchQuery("");
                }}
                className="border-gray-700 hover:bg-gray-800 hover:text-white"
              >
                Cancel
              </Button>
              <Button
                onClick={handleAddMember}
                disabled={!selectedUser || addMemberMutation.isPending}
                className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-600/80 hover:to-purple-600/80 border-none"
              >
                {addMemberMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Adding...
                  </>
                ) : (
                  <>
                    <PlusCircle className="h-4 w-4 mr-2" />
                    Add Member
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
      
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
        <CardHeader className="border-b border-gray-800/50">
          <CardTitle className="flex items-center">
            <div className="h-6 w-6 rounded-full bg-yellow-900/30 flex items-center justify-center mr-2">
              <Crown className="h-3.5 w-3.5 text-yellow-400" />
            </div>
            Owner
          </CardTitle>
          <CardDescription>
            The owner has full control over the hub
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-4">
          <div className="p-4 rounded-lg bg-gray-900/30 border border-gray-800/50">
            <div className="flex items-center gap-4">
              <div className="h-12 w-12 rounded-full border-2 border-yellow-500/20 overflow-hidden">
                <Image
                  src={
                    members?.owner.image ||
                    "https://api.dicebear.com/7.x/shapes/svg?seed=owner"
                  }
                  alt={members?.owner.name || "Owner"}
                  width={48}
                  height={48}
                  className="object-cover"
                  style={{ width: "100%", height: "100%" }}
                  unoptimized
                />
              </div>
              <div>
                <div className="font-medium text-lg">
                  {members?.owner.name || "Unknown"}
                </div>
                <div className="flex items-center text-sm text-yellow-400">
                  <Crown className="h-3.5 w-3.5 mr-1.5" />
                  Owner
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
        <CardHeader className="border-b border-gray-800/50">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div>
              <CardTitle className="flex items-center">
                <div className="h-6 w-6 rounded-full bg-purple-900/30 flex items-center justify-center mr-2">
                  <Shield className="h-3.5 w-3.5 text-purple-400" />
                </div>
                Moderators & Managers
              </CardTitle>
              <CardDescription>
                Managers can edit hub settings and manage members. Moderators
                can only moderate content.
              </CardDescription>
            </div>
            <Button
              onClick={() => setIsDialogOpen(true)}
              className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-600/80 hover:to-purple-600/80 border-none w-full sm:w-auto"
            >
              <UserPlus className="h-4 w-4 mr-2" />
              Add Member
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pt-4">
          {members?.moderators && members.moderators.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {members.moderators.map((mod) => (
                <div
                  key={mod.id}
                  className="flex items-center justify-between p-4 rounded-lg bg-gray-900/30 border border-gray-800/50"
                >
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full border-2 border-purple-500/20 overflow-hidden">
                      <Image
                        src={
                          mod.user?.image ||
                          "https://api.dicebear.com/7.x/shapes/svg?seed=mod"
                        }
                        alt={mod.user.name || "Moderator"}
                        width={40}
                        height={40}
                        className="object-cover"
                        style={{ width: "100%", height: "100%" }}
                        unoptimized
                      />
                    </div>
                    <div>
                      <div className="font-medium">{mod.user.name}</div>
                      <div className="flex items-center text-xs">
                        {mod.role === "MANAGER" ? (
                          <span className="text-blue-400 flex items-center">
                            <Shield className="h-3 w-3 mr-1" />
                            Manager
                          </span>
                        ) : (
                          <span className="text-purple-400 flex items-center">
                            <Shield className="h-3 w-3 mr-1" />
                            Moderator
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      align="end"
                      className="bg-gray-900 border-gray-800"
                    >
                      <DropdownMenuItem
                        onClick={() =>
                          handleUpdateRole(
                            mod.id,
                            mod.role === "MANAGER" ? "MODERATOR" : "MANAGER"
                          )
                        }
                        className="cursor-pointer hover:bg-gray-800"
                      >
                        <Shield className="h-4 w-4 mr-2" />
                        {mod.role === "MANAGER"
                          ? "Change to Moderator"
                          : "Change to Manager"}
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleRemoveMember(mod.id)}
                        className="cursor-pointer text-red-500 focus:text-red-500 hover:bg-red-950/30"
                      >
                        <Trash className="h-4 w-4 mr-2" />
                        Remove
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 bg-gray-900/30 rounded-lg border border-gray-800/50 p-6">
              <Shield className="h-12 w-12 mx-auto text-gray-500 mb-4" />
              <h3 className="text-lg font-medium mb-2">No Members Yet</h3>
              <p className="text-gray-400 mb-4">
                No moderators or managers have been added yet.
              </p>
              <Button
                onClick={() => setIsDialogOpen(true)}
                className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-600/80 hover:to-purple-600/80 border-none"
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Add Member
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </>
  );
}
