'use client';

import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import {
  AlertTriangle,
  Bell,
  ChevronLeft,
  ChevronRight,
  Edit,
  FileText,
  Gavel,
  Globe,
  Home,
  MessageSquare,
  ScrollText,
  Settings,
  Shield,
  Users,
} from 'lucide-react';
import { motion } from 'motion/react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';

interface HubSidebarProps {
  hubId: string;
  canModerate?: boolean;
  canEdit?: boolean;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

interface SidebarNavItemProps {
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  active: boolean;
  isCollapsed: boolean;
  color?: 'indigo' | 'blue' | 'green' | 'purple' | 'red' | 'pink' | 'orange' | 'yellow';
}

interface NavigationItem {
  value: string;
  label: string;
  color: 'indigo' | 'blue' | 'green' | 'purple' | 'red' | 'pink' | 'orange' | 'yellow';
  icon: React.ComponentType<{ className?: string }>;
  href: string;
  show: boolean | ((permissions: { canModerate: boolean; canEdit: boolean }) => boolean);
}

interface SidebarSection {
  key: string;
  title?: string;
  defaultOpen?: boolean;
  items: NavigationItem[];
}

function SidebarNavItem({
  href,
  icon: Icon,
  label,
  active,
  isCollapsed,
  color = 'indigo',
}: SidebarNavItemProps) {
  const colorMap = {
    indigo: 'text-white',
    blue: 'text-white',
    green: 'text-white',
    purple: 'text-white',
    red: 'text-white',
    pink: 'text-white',
    orange: 'text-white',
    yellow: 'text-white',
  };

  const iconColorMap = {
    indigo: 'bg-[var(--color-brand-indigo-500)]/20 text-[var(--color-brand-indigo-400)]',
    blue: 'bg-[var(--color-brand-blue-500)]/20 text-[var(--color-brand-blue-400)]',
    green: 'bg-[var(--color-brand-emerald-500)]/20 text-[var(--color-brand-emerald-400)]',
    purple: 'bg-[var(--color-brand-purple-500)]/20 text-[var(--color-brand-purple-400)]',
    red: 'bg-red-500/20 text-red-400',
    pink: 'bg-[var(--color-brand-pink-500)]/20 text-[var(--color-brand-pink-400)]',
    orange: 'bg-[var(--color-brand-orange-500)]/20 text-[var(--color-brand-orange-400)]',
    yellow: 'bg-yellow-500/20 text-yellow-400',
  };

  const activeBackgroundMap = {
    indigo: 'from-[var(--color-brand-indigo-500)]/20 via-[var(--color-brand-indigo-600)]/10 to-[var(--color-brand-indigo-500)]/5',
    blue: 'from-[var(--color-brand-blue-500)]/20 via-[var(--color-brand-blue-600)]/10 to-[var(--color-brand-blue-500)]/5',
    green: 'from-[var(--color-brand-emerald-500)]/20 via-[var(--color-brand-emerald-600)]/10 to-[var(--color-brand-emerald-500)]/5',
    purple: 'from-[var(--color-brand-purple-500)]/20 via-[var(--color-brand-purple-600)]/10 to-[var(--color-brand-purple-500)]/5',
    red: 'from-red-500/20 via-red-600/10 to-red-500/5',
    pink: 'from-[var(--color-brand-pink-500)]/20 via-[var(--color-brand-pink-600)]/10 to-[var(--color-brand-pink-500)]/5',
    orange: 'from-[var(--color-brand-orange-500)]/20 via-[var(--color-brand-orange-600)]/10 to-[var(--color-brand-orange-500)]/5',
    yellow: 'from-yellow-500/20 via-yellow-600/10 to-yellow-500/5',
  };

  const activeIndicatorMap = {
    indigo: 'bg-[var(--color-brand-indigo-500)]',
    blue: 'bg-[var(--color-brand-blue-500)]',
    green: 'bg-[var(--color-brand-emerald-500)]',
    purple: 'bg-[var(--color-brand-purple-500)]',
    red: 'bg-red-500',
    pink: 'bg-[var(--color-brand-pink-500)]',
    orange: 'bg-[var(--color-brand-orange-500)]',
    yellow: 'bg-yellow-500',
  };

  const content = (
    <Link
      href={href}
      className={cn(
        'flex items-center px-3 py-2.5 text-sm transition-all relative group',
        isCollapsed ? 'justify-center' : 'justify-between',
        active
          ? `${colorMap[color]} font-medium bg-gradient-to-r ${activeBackgroundMap[color]} backdrop-blur-sm border border-white/10`
          : 'text-gray-400 hover:text-white hover:bg-gray-800/50',
      )}
      style={{
        borderRadius: 'var(--radius)', // 8px for cards
      }}
    >
      <div className={cn('flex items-center', isCollapsed ? 'justify-center' : 'gap-3')}>
        <div
          className={cn(
            'flex items-center justify-center w-7 h-7 transition-all duration-200',
            active ? iconColorMap[color] : 'text-gray-400 group-hover:text-gray-300',
          )}
          style={{
            borderRadius: 'var(--radius-button)', // 12px for buttons
          }}
        >
          <Icon className="h-4 w-4" />
        </div>
        {!isCollapsed && <span className="font-medium">{label}</span>}
      </div>
      {active && (
        <motion.div
          layoutId="hubActiveIndicator"
          className={cn(
            'absolute right-0 w-1 h-8',
            activeIndicatorMap[color],
          )}
          style={{
            borderRadius: '4px 0 0 4px',
          }}
          initial={{ opacity: 0, scaleY: 0.5 }}
          animate={{ opacity: 1, scaleY: 1 }}
          transition={{ duration: 0.3, type: "spring", stiffness: 300 }}
        />
      )}

      {/* Subtle glow effect on hover */}
      {active && (
        <div
          className={cn(
            'absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none',
            `bg-gradient-to-r ${activeBackgroundMap[color]}`,
          )}
          style={{
            borderRadius: 'var(--radius)',
          }}
        />
      )}
    </Link>
  );

  if (isCollapsed) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>{content}</TooltipTrigger>
          <TooltipContent side="right" className="ml-2">
            <p>{label}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return content;
}

interface SidebarSectionProps {
  title: string;
  children: React.ReactNode;
  isCollapsed: boolean;
  defaultOpen?: boolean;
}

function SidebarSection({ title, children, isCollapsed, defaultOpen = true }: SidebarSectionProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  if (isCollapsed) {
    return <div className="space-y-1">{children}</div>;
  }

  return (
    <div className="py-3">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between px-3 py-2 text-xs font-bold text-gray-400 uppercase tracking-wider hover:text-gray-300 transition-all duration-200 group"
      >
        <span className="flex items-center gap-2">
          <div className="w-4 h-0.5 bg-gradient-to-r from-[var(--color-brand-purple-500)] to-transparent opacity-60 group-hover:opacity-100 transition-opacity" />
          {title}
        </span>
        <motion.div
          animate={{ rotate: isOpen ? 90 : 0 }}
          transition={{ duration: 0.3, type: "spring", stiffness: 300 }}
          className="text-gray-500 group-hover:text-gray-400"
        >
          <ChevronRight className="h-3.5 w-3.5" />
        </motion.div>
      </button>
      <motion.div
        className="mt-2 space-y-1 overflow-hidden"
        initial={{ height: defaultOpen ? 'auto' : 0, opacity: defaultOpen ? 1 : 0 }}
        animate={{
          height: isOpen ? 'auto' : 0,
          opacity: isOpen ? 1 : 0
        }}
        transition={{
          duration: 0.3,
          type: "spring",
          stiffness: 300,
          opacity: { duration: 0.2 }
        }}
      >
        {children}
      </motion.div>
    </div>
  );
}

export function HubSidebar({
  hubId,
  canModerate = false,
  canEdit = false,
  isCollapsed = false,
  onToggleCollapse,
}: HubSidebarProps) {
  const pathname = usePathname();

  // Configuration for all sidebar sections and items
  const sidebarConfig: SidebarSection[] = [
    {
      key: 'main',
      items: [
        {
          value: 'overview',
          label: 'Overview',
          color: 'indigo',
          icon: MessageSquare,
          href: `/dashboard/hubs/${hubId}`,
          show: true,
        },
        {
          value: 'discovery',
          label: 'Discoverability',
          color: 'yellow',
          icon: Globe,
          href: `/dashboard/hubs/${hubId}/discoverability`,
          show: ({ canEdit }) => canEdit,
        },
      ],
    },
    {
      key: 'management',
      title: 'Management',
      defaultOpen: true,
      items: [
        {
          value: 'edit',
          label: 'Edit Hub',
          color: 'blue',
          icon: Edit,
          href: `/dashboard/hubs/${hubId}/edit`,
          show: ({ canEdit }) => canEdit,
        },
        {
          value: 'members',
          label: 'Members',
          color: 'blue',
          icon: Users,
          href: `/dashboard/hubs/${hubId}/members`,
          show: ({ canModerate }) => canModerate,
        },
        {
          value: 'connections',
          label: 'Connections',
          color: 'green',
          icon: Home,
          href: `/dashboard/hubs/${hubId}/connections`,
          show: ({ canModerate }) => canModerate,
        },
        {
          value: 'logging',
          label: 'Logging',
          color: 'purple',
          icon: FileText,
          href: `/dashboard/hubs/${hubId}/logging`,
          show: ({ canEdit }) => canEdit,
        },
      ],
    },
    {
      key: 'moderation',
      title: 'Moderation',
      defaultOpen: true,
      items: [
        {
          value: 'reports',
          label: 'Reports',
          color: 'red',
          icon: Shield,
          href: `/dashboard/hubs/${hubId}/reports`,
          show: ({ canModerate }) => canModerate,
        },
        {
          value: 'appeals',
          label: 'Appeals',
          color: 'pink',
          icon: Bell,
          href: `/dashboard/hubs/${hubId}/appeals`,
          show: ({ canModerate }) => canModerate,
        },
        {
          value: 'infractions',
          label: 'Infractions',
          color: 'orange',
          icon: Gavel,
          href: `/dashboard/hubs/${hubId}/infractions`,
          show: ({ canModerate }) => canModerate,
        },
        {
          value: 'anti-swear',
          label: 'Anti-Swear',
          color: 'red',
          icon: AlertTriangle,
          href: `/dashboard/hubs/${hubId}/anti-swear`,
          show: ({ canModerate }) => canModerate,
        },
      ],
    },
    {
      key: 'misc',
      items: [
        {
          value: 'settings',
          label: 'Settings',
          color: 'orange',
          icon: Settings,
          href: `/dashboard/hubs/${hubId}/settings`,
          show: true,
        },
      ],
    },
  ];

  const permissions = { canModerate, canEdit };

  // Filter and process sections based on permissions
  const visibleSections = sidebarConfig
    .map((section) => ({
      ...section,
      items: section.items.filter((item) =>
        typeof item.show === 'function' ? item.show(permissions) : item.show,
      ),
    }))
    .filter((section) => section.items.length > 0);

  return (
    <div
      className="flex flex-col h-full backdrop-blur-xl border-r shadow-2xl relative overflow-hidden"
      style={{
        background: `
          linear-gradient(135deg,
            rgba(17, 24, 39, 0.95) 0%,
            rgba(31, 41, 55, 0.98) 50%,
            rgba(17, 24, 39, 0.95) 100%
          ),
          var(--bg-gradient-primary)
        `,
        borderColor: 'rgba(var(--color-brand-purple-400-rgb), 0.1)',
      }}
    >
      {/* Animated background glow */}
      <div
        className="absolute inset-0 opacity-5"
        style={{
          background: `radial-gradient(circle at 20% 20%,
            rgba(var(--color-brand-purple-500-rgb), 0.3) 0%,
            transparent 70%
          )`
        }}
      />

      {/* Header */}
      <div
        className="flex items-center justify-between p-4 border-b flex-shrink-0 relative backdrop-blur-sm"
        style={{
          borderColor: 'rgba(var(--color-brand-purple-400-rgb), 0.1)',
          background: 'rgba(var(--color-brand-purple-900-rgb), 0.1)',
        }}
      >
        {!isCollapsed && (
          <h2 className="text-md font-bold gap-2 flex items-center text-white">
            <div className="w-2 h-2 rounded-full bg-gradient-to-r from-[var(--color-brand-purple-500)] to-[var(--color-brand-purple-600)] shadow-lg" />
            Hub Dashboard
          </h2>
        )}
        {onToggleCollapse && (
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggleCollapse}
            className="h-9 w-9 text-gray-400 hover:text-white hover:bg-white/10 flex-shrink-0 border border-transparent hover:border-white/20 transition-all duration-200 shadow-sm cursor-pointer backdrop-blur-sm"
            style={{ borderRadius: 'var(--radius)' }}
            title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        )}
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto p-4 space-y-2 hub-sidebar-scrollbar relative">
        {visibleSections.map((section) => {
          const sectionItems = section.items.map((item) => (
            <SidebarNavItem
              key={item.value}
              href={item.href}
              icon={item.icon}
              label={item.label}
              active={pathname === item.href}
              isCollapsed={isCollapsed}
              color={item.color}
            />
          ));

          // If section has no title, render items directly (like main and misc)
          if (!section.title) {
            return (
              <div key={section.key} className="space-y-1.5">
                {sectionItems}
              </div>
            );
          }

          // If section has a title, render as collapsible section
          return (
            <SidebarSection
              key={section.key}
              title={section.title}
              isCollapsed={isCollapsed}
              defaultOpen={section.defaultOpen}
            >
              <div className="space-y-1.5">
                {sectionItems}
              </div>
            </SidebarSection>
          );
        })}
      </div>
    </div>
  );
}
