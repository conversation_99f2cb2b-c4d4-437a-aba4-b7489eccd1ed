'use client';

import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import {
  AlertTriangle,
  Bell,
  ChevronLeft,
  ChevronRight,
  Edit,
  FileText,
  Gavel,
  Globe,
  Home,
  MessageSquare,
  ScrollText,
  Settings,
  Shield,
  Users,
} from 'lucide-react';
import { motion } from 'motion/react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';

interface HubSidebarProps {
  hubId: string;
  canModerate?: boolean;
  canEdit?: boolean;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

interface SidebarNavItemProps {
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  active: boolean;
  isCollapsed: boolean;
  color?: 'indigo' | 'blue' | 'green' | 'purple' | 'red' | 'pink' | 'orange' | 'yellow';
}

interface NavigationItem {
  value: string;
  label: string;
  color: 'indigo' | 'blue' | 'green' | 'purple' | 'red' | 'pink' | 'orange' | 'yellow';
  icon: React.ComponentType<{ className?: string }>;
  href: string;
  show: boolean | ((permissions: { canModerate: boolean; canEdit: boolean }) => boolean);
}

interface SidebarSection {
  key: string;
  title?: string;
  defaultOpen?: boolean;
  items: NavigationItem[];
}

function SidebarNavItem({
  href,
  icon: Icon,
  label,
  active,
  isCollapsed,
  color = 'indigo',
}: SidebarNavItemProps) {
  const colorMap = {
    indigo: 'bg-gradient-to-r from-indigo-500/20 to-purple-500/10 text-white',
    blue: 'bg-gradient-to-r from-blue-500/20 to-indigo-500/10 text-white',
    green: 'bg-gradient-to-r from-green-500/20 to-emerald-500/10 text-white',
    purple: 'bg-gradient-to-r from-purple-500/20 to-pink-500/10 text-white',
    red: 'bg-gradient-to-r from-red-500/20 to-pink-500/10 text-white',
    pink: 'bg-gradient-to-r from-pink-500/20 to-rose-500/10 text-white',
    orange: 'bg-gradient-to-r from-orange-500/20 to-red-500/10 text-white',
    yellow: 'bg-gradient-to-r from-yellow-500/20 to-orange-500/10 text-white',
  };

  const iconColorMap = {
    indigo: 'bg-indigo-500/20 text-indigo-400',
    blue: 'bg-blue-500/20 text-blue-400',
    green: 'bg-green-500/20 text-green-400',
    purple: 'bg-purple-500/20 text-purple-400',
    red: 'bg-red-500/20 text-red-400',
    pink: 'bg-pink-500/20 text-pink-400',
    orange: 'bg-orange-500/20 text-orange-400',
    yellow: 'bg-yellow-500/20 text-yellow-400',
  };

  const content = (
    <Link
      href={href}
      className={cn(
        'flex items-center rounded-lg px-3 py-2.5 text-sm transition-all relative',
        isCollapsed ? 'justify-center' : 'justify-between',
        active
          ? `${colorMap[color]} font-medium`
          : 'text-gray-400 hover:text-white hover:bg-gray-800/50',
      )}
    >
      <div className={cn('flex items-center', isCollapsed ? 'justify-center' : 'gap-3')}>
        <div
          className={cn(
            'flex items-center justify-center w-6 h-6 rounded-md',
            active ? iconColorMap[color] : 'text-gray-400',
          )}
        >
          <Icon className="h-4 w-4" />
        </div>
        {!isCollapsed && <span>{label}</span>}
      </div>
      {active && (
        <motion.div
          layoutId="hubActiveIndicator"
          className={cn(
            'absolute right-0 w-1 h-8 rounded-l-full',
            active ? `bg-${color}-500` : 'bg-transparent',
          )}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
        />
      )}
    </Link>
  );

  if (isCollapsed) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>{content}</TooltipTrigger>
          <TooltipContent side="right" className="ml-2">
            <p>{label}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return content;
}

interface SidebarSectionProps {
  title: string;
  children: React.ReactNode;
  isCollapsed: boolean;
  defaultOpen?: boolean;
}

function SidebarSection({ title, children, isCollapsed, defaultOpen = true }: SidebarSectionProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  if (isCollapsed) {
    return <div className="space-y-1">{children}</div>;
  }

  return (
    <div className="py-2">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between px-3 py-1.5 text-xs font-semibold text-gray-400 uppercase tracking-wider hover:text-gray-300 transition-colors"
      >
        <span>{title}</span>
        <motion.div animate={{ rotate: isOpen ? 90 : 0 }} transition={{ duration: 0.2 }}>
          <ChevronRight className="h-3.5 w-3.5" />
        </motion.div>
      </button>
      <motion.div
        className="mt-1 space-y-1 overflow-hidden"
        initial={{ height: defaultOpen ? 'auto' : 0 }}
        animate={{ height: isOpen ? 'auto' : 0 }}
        transition={{ duration: 0.2 }}
      >
        {children}
      </motion.div>
    </div>
  );
}

export function HubSidebar({
  hubId,
  canModerate = false,
  canEdit = false,
  isCollapsed = false,
  onToggleCollapse,
}: HubSidebarProps) {
  const pathname = usePathname();

  // Configuration for all sidebar sections and items
  const sidebarConfig: SidebarSection[] = [
    {
      key: 'main',
      items: [
        {
          value: 'overview',
          label: 'Overview',
          color: 'indigo',
          icon: MessageSquare,
          href: `/dashboard/hubs/${hubId}`,
          show: true,
        },
        {
          value: 'discovery',
          label: 'Discoverability',
          color: 'yellow',
          icon: Globe,
          href: `/dashboard/hubs/${hubId}/discoverability`,
          show: ({ canEdit }) => canEdit,
        },
      ],
    },
    {
      key: 'management',
      title: 'Management',
      defaultOpen: true,
      items: [
        {
          value: 'edit',
          label: 'Edit Hub',
          color: 'blue',
          icon: Edit,
          href: `/dashboard/hubs/${hubId}/edit`,
          show: ({ canEdit }) => canEdit,
        },
        {
          value: 'members',
          label: 'Members',
          color: 'blue',
          icon: Users,
          href: `/dashboard/hubs/${hubId}/members`,
          show: ({ canModerate }) => canModerate,
        },
        {
          value: 'connections',
          label: 'Connections',
          color: 'green',
          icon: Home,
          href: `/dashboard/hubs/${hubId}/connections`,
          show: ({ canModerate }) => canModerate,
        },
        {
          value: 'logging',
          label: 'Logging',
          color: 'purple',
          icon: FileText,
          href: `/dashboard/hubs/${hubId}/logging`,
          show: ({ canEdit }) => canEdit,
        },
      ],
    },
    {
      key: 'moderation',
      title: 'Moderation',
      defaultOpen: true,
      items: [
        {
          value: 'reports',
          label: 'Reports',
          color: 'red',
          icon: Shield,
          href: `/dashboard/hubs/${hubId}/reports`,
          show: ({ canModerate }) => canModerate,
        },
        {
          value: 'appeals',
          label: 'Appeals',
          color: 'pink',
          icon: Bell,
          href: `/dashboard/hubs/${hubId}/appeals`,
          show: ({ canModerate }) => canModerate,
        },
        {
          value: 'infractions',
          label: 'Infractions',
          color: 'orange',
          icon: Gavel,
          href: `/dashboard/hubs/${hubId}/infractions`,
          show: ({ canModerate }) => canModerate,
        },
        {
          value: 'anti-swear',
          label: 'Anti-Swear',
          color: 'red',
          icon: AlertTriangle,
          href: `/dashboard/hubs/${hubId}/anti-swear`,
          show: ({ canModerate }) => canModerate,
        },
      ],
    },
    {
      key: 'misc',
      items: [
        {
          value: 'settings',
          label: 'Settings',
          color: 'orange',
          icon: Settings,
          href: `/dashboard/hubs/${hubId}/settings`,
          show: true,
        },
      ],
    },
  ];

  const permissions = { canModerate, canEdit };

  // Filter and process sections based on permissions
  const visibleSections = sidebarConfig
    .map((section) => ({
      ...section,
      items: section.items.filter((item) =>
        typeof item.show === 'function' ? item.show(permissions) : item.show,
      ),
    }))
    .filter((section) => section.items.length > 0);

  return (
    <div
      className="flex flex-col h-full bg-gradient-to-b from-gray-900 to-gray-950 border-r border-gray-800/50 shadow-lg"
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-800/50 flex-shrink-0 bg-gray-900/50">
        {!isCollapsed && <h2 className="text-md font-bold gap-2 flex items-center">Dashboard</h2>}
        {onToggleCollapse && (
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggleCollapse}
            className="h-9 w-9 text-gray-400 hover:text-white hover:bg-gray-800/50 flex-shrink-0 rounded-lg border border-transparent hover:border-gray-700/50 transition-all duration-200 shadow-sm cursor-pointer"
            title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        )}
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto p-3 space-y-1 hub-sidebar-scrollbar">
        {visibleSections.map((section) => {
          const sectionItems = section.items.map((item) => (
            <SidebarNavItem
              key={item.value}
              href={item.href}
              icon={item.icon}
              label={item.label}
              active={pathname === item.href}
              isCollapsed={isCollapsed}
              color={item.color}
            />
          ));

          // If section has no title, render items directly (like main and misc)
          if (!section.title) {
            return (
              <div key={section.key} className="space-y-1">
                {sectionItems}
              </div>
            );
          }

          // If section has a title, render as collapsible section
          return (
            <SidebarSection
              key={section.key}
              title={section.title}
              isCollapsed={isCollapsed}
              defaultOpen={section.defaultOpen}
            >
              {sectionItems}
            </SidebarSection>
          );
        })}
      </div>
    </div>
  );
}
