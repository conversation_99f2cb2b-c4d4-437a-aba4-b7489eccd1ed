"use client";

import { Button } from '@/components/ui/button';
import { motion, AnimatePresence } from 'motion/react';
import { AlertTriangle, RotateCcw, Save, Loader2 } from 'lucide-react';

interface UnsavedChangesPromptProps {
  isVisible: boolean;
  onSave: () => void;
  onReset: () => void;
  isSubmitting?: boolean;
  saveLabel?: string;
  resetLabel?: string;
  message?: string;
}

export function UnsavedChangesPrompt({
  isVisible,
  onSave,
  onReset,
  isSubmitting = false,
  saveLabel = "Save Changes",
  resetLabel = "Discard",
  message = "You have unsaved changes that will be lost if you leave this page.",
}: UnsavedChangesPromptProps) {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: 100, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: 100, scale: 0.95 }}
          transition={{ 
            type: "spring", 
            stiffness: 300, 
            damping: 30,
            opacity: { duration: 0.2 }
          }}
          className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50 max-w-md w-full mx-4"
        >
          <div 
            className="relative backdrop-blur-xl border shadow-2xl overflow-hidden"
            style={{
              borderRadius: 'var(--radius-modal)', // 16px for modals
              background: `
                linear-gradient(135deg, 
                  rgba(var(--color-brand-orange-500-rgb), 0.95) 0%, 
                  rgba(var(--color-brand-orange-600-rgb), 0.98) 100%
                ),
                var(--bg-gradient-primary)
              `,
              borderColor: 'rgba(var(--color-brand-orange-400-rgb), 0.3)',
              boxShadow: `
                0 25px 50px -12px rgba(0, 0, 0, 0.5),
                0 0 0 1px rgba(var(--color-brand-orange-400-rgb), 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1)
              `
            }}
          >
            {/* Animated background glow */}
            <div 
              className="absolute inset-0 opacity-20"
              style={{
                background: `radial-gradient(circle at 30% 30%, 
                  rgba(var(--color-brand-orange-300-rgb), 0.4) 0%, 
                  transparent 70%
                )`
              }}
            />
            
            {/* Content */}
            <div className="relative p-6">
              <div className="flex items-start gap-4">
                {/* Icon */}
                <motion.div
                  initial={{ rotate: -10, scale: 0.8 }}
                  animate={{ rotate: 0, scale: 1 }}
                  transition={{ delay: 0.1, type: "spring", stiffness: 300 }}
                  className="flex-shrink-0 p-2 rounded-full bg-white/20 backdrop-blur-sm"
                >
                  <AlertTriangle className="h-5 w-5 text-white" />
                </motion.div>

                {/* Message */}
                <div className="flex-1 min-w-0">
                  <motion.h3
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.15 }}
                    className="text-white font-semibold text-sm mb-1"
                  >
                    Unsaved Changes
                  </motion.h3>
                  <motion.p
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                    className="text-white/90 text-xs leading-relaxed"
                  >
                    {message}
                  </motion.p>
                </div>
              </div>

              {/* Actions */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.25 }}
                className="flex items-center justify-end gap-3 mt-4"
              >
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onReset}
                  disabled={isSubmitting}
                  className="text-white/90 hover:text-white hover:bg-white/10 border-white/20 hover:border-white/30 transition-all duration-200"
                  style={{ borderRadius: 'var(--radius-button)' }}
                >
                  <RotateCcw className="h-3.5 w-3.5 mr-1.5" />
                  {resetLabel}
                </Button>
                
                <Button
                  size="sm"
                  onClick={onSave}
                  disabled={isSubmitting}
                  className="bg-white text-orange-600 hover:bg-gray-50 shadow-lg hover:shadow-xl transition-all duration-200 font-medium"
                  style={{ borderRadius: 'var(--radius-button)' }}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-3.5 w-3.5 mr-1.5 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-3.5 w-3.5 mr-1.5" />
                      {saveLabel}
                    </>
                  )}
                </Button>
              </motion.div>
            </div>

            {/* Subtle animated border */}
            <motion.div
              className="absolute inset-0 rounded-[inherit] pointer-events-none"
              style={{
                background: `linear-gradient(45deg, 
                  transparent 0%, 
                  rgba(255, 255, 255, 0.1) 50%, 
                  transparent 100%
                )`,
                backgroundSize: '200% 200%'
              }}
              animate={{
                backgroundPosition: ['0% 0%', '100% 100%', '0% 0%']
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "linear"
              }}
            />
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
